/**
 * JavaScript pro admin rozhraní technologií
 */

document.addEventListener('DOMContentLoaded', function() {
    // Načtení SortableJS knihovny pokud není dostupná
    loadSortableJS().then(function() {
        // Inicializace sortable pro drag & drop
        initSortable();

        // Hromadné akce
        initBulkActions();

        // Potvrzení mazání
        initDeleteConfirmation();

        // Preview obrázků
        initImagePreview();

        // Změna stavu kliknutím
        initStatusToggle();
    });
});

/**
 * Načtení SortableJS knihovny
 */
function loadSortableJS() {
    return new Promise(function(resolve) {
        if (typeof Sortable !== 'undefined') {
            resolve();
            return;
        }

        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js';
        script.onload = function() {
            resolve();
        };
        script.onerror = function() {
            console.warn('Nepodařilo se načíst SortableJS knihovnu');
            resolve(); // Pokračujeme i bez knihovny
        };
        document.head.appendChild(script);
    });
}

/**
 * Inicializace drag & drop řazení
 */
function initSortable() {
    const sortableTable = document.getElementById('sortable-technologie');
    if (!sortableTable) return;

    // Pokud je dostupná knihovna Sortable
    if (typeof Sortable !== 'undefined') {
        const sortable = new Sortable(sortableTable, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onStart: function(evt) {
                // Přidání vizuálního feedbacku při začátku tažení
                evt.item.classList.add('dragging');
                showDragFeedback('Přetáhněte řádek na novou pozici');
            },
            onEnd: function(evt) {
                // Odstranění vizuálního feedbacku
                evt.item.classList.remove('dragging');
                hideDragFeedback();

                // Aktualizace pozic pouze pokud se pozice změnila
                if (evt.oldIndex !== evt.newIndex) {
                    updatePositions();
                }
            }
        });
    } else {
        console.warn('SortableJS knihovna není dostupná - drag & drop nebude fungovat');
    }
}

/**
 * Zobrazení zpětné vazby při drag & drop
 */
function showDragFeedback(message) {
    let feedback = document.getElementById('drag-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.id = 'drag-feedback';
        feedback.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007cff;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 9999;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(feedback);
    }
    feedback.textContent = message;
    feedback.style.display = 'block';
}

/**
 * Skrytí zpětné vazby
 */
function hideDragFeedback() {
    const feedback = document.getElementById('drag-feedback');
    if (feedback) {
        feedback.style.display = 'none';
    }
}

/**
 * Aktualizace pozic po drag & drop
 */
function updatePositions() {
    const rows = document.querySelectorAll('#sortable-technologie tr[data-id]');
    const positions = {};

    // Zobrazení loading indikátoru
    showLoadingIndicator('Ukládání nového pořadí...');

    rows.forEach((row, index) => {
        const id = row.getAttribute('data-id');
        positions[id] = index + 1;

        // Aktualizace zobrazené pozice
        const badge = row.querySelector('.position-badge');
        if (badge) {
            badge.textContent = index + 1;
        }
    });

    // AJAX požadavek na server
    const currentUrl = new URL(window.location.href);
    const token = window.token || currentUrl.searchParams.get('token') || '';
    const adminUrl = currentUrl.origin + currentUrl.pathname + '?controller=AdminTechnologie&token=' + token;

    console.log('Sending AJAX request to:', adminUrl);
    console.log('Positions data:', positions);

    fetch(adminUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            'ajax': '1',
            'action': 'updatePositions',
            'positions': JSON.stringify(positions),
            'token': token
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingIndicator();
        if (data.success) {
            showNotification('Pořadí bylo úspěšně aktualizováno', 'success');
        } else {
            showNotification('Chyba při aktualizaci pořadí: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoadingIndicator();
        showNotification('Chyba při komunikaci se serverem', 'error');
        console.error('Error:', error);
    });
}

/**
 * Inicializace hromadných akcí
 */
function initBulkActions() {
    const selectAll = document.getElementById('select-all');
    const rowSelectors = document.querySelectorAll('.row-selector');
    const bulkActions = document.querySelector('.bulk-actions');
    const bulkActionBtn = document.getElementById('bulk-action-btn');

    if (!selectAll || !bulkActions) return;

    // Select all checkbox
    selectAll.addEventListener('change', function() {
        rowSelectors.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        toggleBulkActions();
    });

    // Individual checkboxes
    rowSelectors.forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkActions);
    });

    // Bulk action button
    if (bulkActionBtn) {
        bulkActionBtn.addEventListener('click', executeBulkAction);
    }

    function toggleBulkActions() {
        const checkedBoxes = document.querySelectorAll('.row-selector:checked');
        if (checkedBoxes.length > 0) {
            bulkActions.style.display = 'block';
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

/**
 * Provedení hromadné akce
 */
function executeBulkAction() {
    const actionSelect = document.getElementById('bulk-action-select');
    const checkedBoxes = document.querySelectorAll('.row-selector:checked');

    if (!actionSelect.value) {
        showNotification('Vyberte akci', 'warning');
        return;
    }

    if (checkedBoxes.length === 0) {
        showNotification('Nevybrali jste žádné položky', 'warning');
        return;
    }

    const action = actionSelect.value;
    const ids = Array.from(checkedBoxes).map(cb => cb.value);

    // Potvrzení pro mazání
    if (action === 'delete') {
        if (!confirm(`Opravdu chcete smazat ${ids.length} vybraných technologií?`)) {
            return;
        }
    }

    // Odeslání formuláře
    const form = document.getElementById('bulk-action-form');
    document.getElementById('bulk-action-type').value = action;
    document.getElementById('selected-ids').value = ids.join(',');
    form.submit();
}

/**
 * Inicializace potvrzení mazání
 */
function initDeleteConfirmation() {
    const deleteLinks = document.querySelectorAll('.delete-link');

    deleteLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!confirm('Opravdu chcete smazat tuto technologii?')) {
                e.preventDefault();
            }
        });
    });
}

/**
 * Inicializace preview obrázků
 */
function initImagePreview() {
    const imageInput = document.querySelector('input[type="file"][accept*="image"]');
    if (!imageInput) return;

    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Kontrola typu souboru
        if (!file.type.startsWith('image/')) {
            showNotification('Vyberte platný obrázek', 'error');
            return;
        }

        // Kontrola velikosti (2MB)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('Obrázek je příliš velký (max 2MB)', 'error');
            return;
        }

        // Vytvoření preview
        const reader = new FileReader();
        reader.onload = function(e) {
            showImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
    });
}

/**
 * Zobrazení preview obrázku
 */
function showImagePreview(src) {
    // Odstranění starého preview
    const oldPreview = document.querySelector('.image-preview');
    if (oldPreview) {
        oldPreview.remove();
    }

    // Vytvoření nového preview
    const preview = document.createElement('div');
    preview.className = 'image-preview mt-3';
    preview.innerHTML = `
        <p><strong>Náhled nového obrázku:</strong></p>
        <img src="${src}" class="img-thumbnail" style="max-width: 200px; max-height: 200px;" />
    `;

    // Přidání za file input
    const fileInput = document.querySelector('input[type="file"][accept*="image"]');
    fileInput.parentNode.appendChild(preview);
}

/**
 * Zobrazení notifikace
 */
function showNotification(message, type = 'info') {
    // Pokud existuje PrestaShop notifikační systém
    if (typeof showSuccessMessage === 'function' && type === 'success') {
        showSuccessMessage(message);
        return;
    }

    if (typeof showErrorMessage === 'function' && type === 'error') {
        showErrorMessage(message);
        return;
    }

    // Fallback - jednoduchá notifikace
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Přidání na začátek stránky
    const container = document.querySelector('.content-wrapper') || document.body;
    container.insertBefore(notification, container.firstChild);

    // Automatické skrytí po 5 sekundách
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

/**
 * Inicializace přepínání stavu kliknutím na badge
 */
function initStatusToggle() {
    const statusBadges = document.querySelectorAll('.status-toggle');

    statusBadges.forEach(function(badge) {
        badge.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const currentStatus = this.getAttribute('data-status');
            const newStatus = currentStatus === '1' ? '0' : '1';

            toggleTechnologieStatus(id, newStatus, this);
        });
    });
}

/**
 * Přepnutí stavu technologie
 */
function toggleTechnologieStatus(id, newStatus, badgeElement) {
    showLoadingIndicator('Změna stavu...');

    const currentUrl = new URL(window.location.href);
    const token = window.token || currentUrl.searchParams.get('token') || '';
    const adminUrl = currentUrl.origin + currentUrl.pathname + '?controller=AdminTechnologie&token=' + token;

    console.log('Toggling status for ID:', id, 'to status:', newStatus);
    console.log('Sending AJAX request to:', adminUrl);

    fetch(adminUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            'ajax': '1',
            'action': 'toggleStatus',
            'id': id,
            'status': newStatus,
            'token': token
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingIndicator();
        if (data.success) {
            // Aktualizace badge
            if (newStatus === '1') {
                badgeElement.className = 'badge badge-success status-toggle';
                badgeElement.textContent = 'Aktivní';
                badgeElement.title = 'Klikněte pro deaktivaci';
            } else {
                badgeElement.className = 'badge badge-danger status-toggle';
                badgeElement.textContent = 'Neaktivní';
                badgeElement.title = 'Klikněte pro aktivaci';
            }
            badgeElement.setAttribute('data-status', newStatus);
            badgeElement.style.cursor = 'pointer';

            showSuccessMessage('Stav byl úspěšně změněn');
        } else {
            showErrorMessage('Chyba při změně stavu: ' + data.message);
        }
    })
    .catch(error => {
        hideLoadingIndicator();
        showErrorMessage('Chyba při komunikaci se serverem');
        console.error('Error:', error);
    });
}

/**
 * Zobrazení loading indikátoru
 */
function showLoadingIndicator(message = 'Načítání...') {
    // Odstranění starého indikátoru
    hideLoadingIndicator();

    const indicator = document.createElement('div');
    indicator.id = 'loading-indicator';
    indicator.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    indicator.innerHTML = `
        <div style="
            background: white;
            padding: 20px 30px;
            border-radius: 5px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            text-align: center;
            font-size: 16px;
        ">
            <div style="
                width: 30px;
                height: 30px;
                border: 3px solid #f3f3f3;
                border-top: 3px solid #007cff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            "></div>
            ${message}
        </div>
    `;

    // Přidání CSS animace
    if (!document.getElementById('loading-spinner-style')) {
        const style = document.createElement('style');
        style.id = 'loading-spinner-style';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(indicator);
}

/**
 * Skrytí loading indikátoru
 */
function hideLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) {
        indicator.remove();
    }
}

/**
 * Zobrazení success zprávy
 */
function showSuccessMessage(message) {
    showNotification(message, 'success');
}

/**
 * Zobrazení error zprávy
 */
function showErrorMessage(message) {
    showNotification(message, 'error');
}
